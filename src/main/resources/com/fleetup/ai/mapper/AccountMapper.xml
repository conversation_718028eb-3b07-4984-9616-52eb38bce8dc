<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fleetup.ai.mapper.AccountMapper">

    <select id="fetchAccountByCondition" resultType="arraylist">
        select ACCT_ID,
               COMPANY_NAME,
               COUNTRY,
                LANGUAGE,
                ADDRESS,
                PHONE,
                TIME_ZONE,
                TIME_ZONE_ID,
                DATA_MODEL,
                MODALITY,
                USDOT_NO,
                STATUS,
                ACCT_TYPE
        from ACCOUNT
        <where>
            <if test="acctId != null">
                and ACCT_ID = #{acctId}
            </if>

            <if test="companyName != null and companyName != ''">
               and  UPPER(COMPANY_NAME) LIKE UPPER('%'||#{companyName}||'%')
            </if>

            <if test="country != null and country != ''">
                and UPPER(COUNTRY) LIKE UPPER('%'||#{country}||'%')
            </if>

            <if test="language != null">
                and LANGUAGE = #{language}
            </if>
            <if test="status != null">
                and STATUS = #{status}
            </if>
            <if test="acctType != null">
                and ACCT_TYPE = #{acctType}
            </if>
        </where>

    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="STDOUT" class="io.symphonia.lambda.logging.DefaultConsoleAppender">
        <encoder>
            <pattern>%X{AWSRequestId} %-5p %c{1} - %m%n</pattern>
        </encoder>
    </appender>
    <logger name="java.sql.Connection" level="info"/>
    <logger name="java.sql.Statement" level="info"/>
    <logger name="java.sql.PreparedStatement" level="info"/>
    <logger name="java.sql.ResultSet" level="info"/>
    <logger name="com.fleetup.ai" level="info"/>
    <logger name="com.fleetup.ai.mapper" level="debug"/>
    <root level="info">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>

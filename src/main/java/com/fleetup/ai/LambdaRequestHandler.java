package com.fleetup.ai;

import com.alibaba.fastjson2.JSON;
import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.fleetup.ai.handler.APIHandler;
import com.fleetup.ai.handler.FetchAccountByConditionHandler;
import com.fleetup.ai.handler.HandlerInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2025-06-30 09:26:04
 */
@Slf4j
public class LambdaRequestHandler implements RequestHandler<Map<String, Object>, Map<String, Object>> {

    private static final List<HandlerInfo> apiHandlers = new ArrayList<>();

    static {
        registerHandler();
    }

    private static void registerHandler() {
        apiHandlers.add(new HandlerInfo("POST", "/fetchAccountsByCondition", new FetchAccountByConditionHandler()));
    }

    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> handleRequest(Map<String, Object> input, Context context) {
        log.info("Input: {}", JSON.toJSONString(input));
        String apiPath = (String) input.get("apiPath");
        String httpMethod = (String) input.get("httpMethod");

        Map<String, Object> output;
        try {
            Optional<HandlerInfo> handlerOpt = apiHandlers.stream().filter(handlerInfo -> handlerInfo.getRequestPath().equals(apiPath)
                            && handlerInfo.getHttpMethod().equalsIgnoreCase(httpMethod))
                    .findFirst();

            HandlerInfo handlerInfo = handlerOpt.orElseThrow(() -> new RuntimeException("Not found handler for " + apiPath));

            APIHandler apiHandler = handlerInfo.getHandler();

            Map<String, Object> params;
            if ("GET".equalsIgnoreCase(httpMethod)) {
                params = convertArguments((List<Map<String, Object>>) input.get("parameters"));
            } else {
                Map<String, Object> requestBody = (Map<String, Object>) input.get("requestBody");
                log.info("requestBody: {}", JSON.toJSONString(requestBody));
                Map<String, Object> content = (Map<String, Object>) requestBody.get("content");
                log.info("content: {}", JSON.toJSONString(content));
                Map<String, Object> application = (Map<String, Object>) content.get("application/json");
                log.info("application: {}", JSON.toJSONString(application));
                List<Map<String, Object>> properties = (List<Map<String, Object>>) application.get("properties");
                log.info("properties: {}", JSON.toJSONString(properties));
                params = convertArguments(properties);
            }
            log.info("Params: {}", JSON.toJSONString(params));
            Map<String, Object> response = apiHandler.handleRequest(params);
            log.info("response: {}", JSON.toJSONString(response));
            output = buildResponse(input, response);

        } catch (RuntimeException e) {
            output = runtimeException(e);
        } catch (Exception e) {
            output = unexpectedException(e);
        }

        log.info("Output: {}", JSON.toJSONString(output));
        return output;
    }



    private Map<String, Object> buildResponse(Map<String, Object> input,  Map<String, Object> response) {
        String apiPath = (String) input.get("apiPath");
        String messageVersion = (String) input.getOrDefault("messageVersion", "1.0");
        String actionGroup = (String) input.get("actionGroup");
        String httpMethod = (String) input.get("httpMethod");

        Map<String, Object> actionResponse = new HashMap<>();
        actionResponse.put("actionGroup", actionGroup);
        actionResponse.put("apiPath", apiPath);
        actionResponse.put("httpMethod", httpMethod);
        actionResponse.put("httpStatusCode", 200);
        actionResponse.put("responseBody", JSON.toJSONString(response));


        Map<String, Object> finalResponse = new HashMap<>();
        finalResponse.put("response", actionResponse);
        finalResponse.put("messageVersion", messageVersion);
        return finalResponse;
    }

    private HashMap<String, Object> runtimeException(RuntimeException e) {
        log.error("Runtime exception: ", e);

        HashMap<String, Object> response = new HashMap<>();
        response.put("statusCode", "500");
        response.put("body", e.getMessage());
        return response;
    }

    private HashMap<String, Object> unexpectedException(Exception e) {
        log.error("Unexpected exception: ", e);

        HashMap<String, Object> response = new HashMap<>();
        response.put("statusCode", "500");
        response.put("body", "Internal server error");
        return response;
    }

    private Map<String, Object> convertArguments(List<Map<String, Object>> list) {
        Map<String, Object> res = new HashMap<>();
        if (list == null || list.isEmpty()) {
            return res;
        }

        for (Map<String, Object> argument : list) {
            String argumentName = (String) argument.get("name");
            Object argumentVal = argument.get("value");
            res.put(argumentName, argumentVal);
            log.info("put {} = {}", argumentName, argumentVal);
        }
        return res;
    }
}

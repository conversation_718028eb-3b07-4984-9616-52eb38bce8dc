package com.fleetup.ai.utils;

import lombok.experimental.UtilityClass;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025-06-30 16:43:04
 */
@UtilityClass
public class RequestParamUtil {


    public String getParam(String key, Map<String, Object> params) {
        Object value = params.get(key);
        return value != null ? value.toString() : null;
    }

    public Integer getParamAsInteger(String key, Map<String, Object> params) {
        String param = getParam(key, params);
        if (param != null) {
            return Integer.parseInt(param);
        }
        return null;
    }

    public Boolean getParamAsBool(String key, Map<String, Object> params) {
        String param = getParam(key, params);
        if (param != null) {
            return Boolean.parseBoolean(param);
        }
        return null;
    }

    public Long getParamAsLong(String key, Map<String, Object> params) {
        String param = getParam(key, params);
        if (param != null) {
            return Long.parseLong(param);
        }
        return null;
    }

    public Double getParamAsDouble(String key, Map<String, Object> params) {
        String param = getParam(key, params);
        if (param != null) {
            return Double.parseDouble(param);
        }
        return null;
    }

}

package com.fleetup.ai.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;

/**
 * @Author: <PERSON>
 * @Email: <a href="mailto:<EMAIL>"><PERSON></a>
 * @Date: 2019/8/23 14:02
 * @Description:
 */
@Slf4j
public class PropertiesUtils {

    private static final Map<String, String> ENVIRONMENT_VARIABLE_MAP = new HashMap<>(32);
    private static final String PROPERTIES_FILE_NAME = "application.properties";
    private static final Properties PROPERTIES;

    static {
        System.getenv().forEach((key, value) -> {
            ENVIRONMENT_VARIABLE_MAP.put(key, value);
            ENVIRONMENT_VARIABLE_MAP.put(convertKeyFromEnv(key), value);
        });
        PROPERTIES = new Properties();
        try {
            PROPERTIES.load(new InputStreamReader(Objects.requireNonNull(PropertiesUtils.class.getClassLoader().getResourceAsStream(PROPERTIES_FILE_NAME)), StandardCharsets.UTF_8));
        } catch (IOException e) {
            log.error("read properties file " + PROPERTIES_FILE_NAME + " exception", e);
        }
    }

    /**
     * get properties
     *
     * @param key
     * @return
     */
    public static String getProperty(String key) {
        if (ENVIRONMENT_VARIABLE_MAP.containsKey(key)) {
            return StringUtils.trim(ENVIRONMENT_VARIABLE_MAP.get(key));
        }

        String value = PROPERTIES.getProperty(key,"");
        if (StringUtils.isBlank(value)) {
            return "";
        }
        return StringUtils.trim(value);
    }

    /**
     * get properties, is not found, return default value
     *
     * @param key
     * @param defaultValue
     * @return
     */
    public static String getProperty(String key, String defaultValue) {
        if (ENVIRONMENT_VARIABLE_MAP.containsKey(key)) {
            return StringUtils.trim(ENVIRONMENT_VARIABLE_MAP.get(key));
        }

        String value = PROPERTIES.getProperty(key);
        if (StringUtils.isBlank(value)) {
            return defaultValue;
        }
        return StringUtils.trim(value);
    }

    private static String convertKeyFromEnv(String environmentVariableKey) {
        return RegExUtils.replaceAll(environmentVariableKey, "_", ".");
    }
}
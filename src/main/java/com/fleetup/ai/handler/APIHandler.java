package com.fleetup.ai.handler;

import com.alibaba.fastjson2.JSON;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025-06-30 15:45:57
 */
public interface APIHandler {

    Map<String, Object> handleRequest(Map<String, Object> input);


    default Map<String, Object> success(Map<String, Object> data) {
        Map<String, Object> res = new HashMap<>();

        Map<String, Object> body = new HashMap<>();
        body.put("body", JSON.toJSONString(data));

        res.put("application/json", body);

        return res;
    }
}

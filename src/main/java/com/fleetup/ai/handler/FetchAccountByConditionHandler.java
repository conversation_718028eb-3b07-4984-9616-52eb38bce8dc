package com.fleetup.ai.handler;

import com.fleetup.ai.factory.MyBatisSessionFactory;
import com.fleetup.ai.mapper.AccountMapper;
import com.fleetup.ai.utils.RequestParamUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025-06-30 15:49:21
 */
public class FetchAccountByConditionHandler implements APIHandler {

    @Override
    public Map<String, Object> handleRequest(Map<String, Object> input) {
        Integer acctId = RequestParamUtil.getParamAsInteger("acctId", input);
        String companyName = RequestParamUtil.getParam("companyName", input);
        String country = RequestParamUtil.getParam("country", input);
        Integer language = RequestParamUtil.getParamAsInteger("language", input);
        Integer status = RequestParamUtil.getParamAsInteger("status", input);
        Integer acctType = RequestParamUtil.getParamAsInteger("acctType", input);

        List<Map<String, Object>> result = MyBatisSessionFactory.session().getMapper(AccountMapper.class)
                .fetchAccountByCondition(acctId, companyName, country, language, status, acctType);
        return success(result);
    }
}

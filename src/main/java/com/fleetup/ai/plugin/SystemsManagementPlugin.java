package com.fleetup.ai.plugin;

import com.amazonaws.AmazonClientException;
import com.amazonaws.auth.profile.ProfileCredentialsProvider;
import com.amazonaws.profile.path.AwsProfileFileLocationProvider;
import com.amazonaws.services.simplesystemsmanagement.AWSSimpleSystemsManagement;
import com.amazonaws.services.simplesystemsmanagement.AWSSimpleSystemsManagementClientBuilder;
import com.amazonaws.services.simplesystemsmanagement.model.GetParametersByPathRequest;
import com.amazonaws.services.simplesystemsmanagement.model.GetParametersByPathResult;
import com.amazonaws.services.simplesystemsmanagement.model.Parameter;

import com.fleetup.ai.PropertiesKey;
import com.fleetup.ai.dto.DatabaseCredential;
import com.fleetup.ai.utils.PropertiesUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Email: <a href="mailto:<EMAIL>"><PERSON></a>
 * @Date: 2019/8/26 14:00
 * @Description:
 */
@Slf4j
public class SystemsManagementPlugin {

    private volatile static SystemsManagementPlugin instance;

    private final AWSSimpleSystemsManagement client;
    private boolean hasShutdown = false;

    private static final String JDBC_URL_SUFFIX = PropertiesUtils.getProperty(PropertiesKey.SSM_JDBC_URL_SUFFIX);
    private static final String JDBC_USERNAME_SUFFIX = PropertiesUtils.getProperty(PropertiesKey.SSM_JDBC_USERNAME_SUFFIX);
    private static final String JDBC_PASSWORD_SUFFIX = PropertiesUtils.getProperty(PropertiesKey.SSM_JDBC_PASSWORD_SUFFIX);

    private SystemsManagementPlugin(String profileName) {
        try {
            if (StringUtils.isBlank(profileName)) {
                client = AWSSimpleSystemsManagementClientBuilder.defaultClient();
            } else {
                client = AWSSimpleSystemsManagementClientBuilder.standard()
                        .withCredentials(new ProfileCredentialsProvider(
                                AwsProfileFileLocationProvider.DEFAULT_CREDENTIALS_LOCATION_PROVIDER.getLocation().getPath(),
                                profileName))
                        .build();
            }
        } catch (Exception e) {
            throw new AmazonClientException("Instancing AmazonSimpleSystemsManagement exception: ", e);
        }
    }

    /**
     * create a new instance
     *
     * @return
     */
    public static SystemsManagementPlugin getInstance() {
        return getInstance(null);
    }

    public static SystemsManagementPlugin getInstance(String profileName) {
        if (instance == null || instance.hasShutdown) {
            synchronized (SystemsManagementPlugin.class) {
                if (instance == null) {
                    instance = new SystemsManagementPlugin(profileName);
                }
            }
        }
        return instance;
    }

    /**
     * get db credential from Systems Management
     *
     * @param path
     * @return
     */
    public GetParametersByPathResult getParameter(String path) {

        GetParametersByPathRequest request = new GetParametersByPathRequest()
                .withWithDecryption(true)
                .withPath(path);
        return client.getParametersByPath(request);
    }

    /**
     * get db credential from Systems Management
     *
     * @param path
     * @return
     */
    public DatabaseCredential getDatabaseCredentialItem(String path) {

        GetParametersByPathRequest request = new GetParametersByPathRequest()
                .withWithDecryption(true)
                .withPath(path);
        List<Parameter> parameters = client.getParametersByPath(request).getParameters();
        DatabaseCredential databaseCredentialItem = new DatabaseCredential();
        parameters.forEach(parameter -> {
            if (StringUtils.endsWith(parameter.getName(), JDBC_URL_SUFFIX)) {
                databaseCredentialItem.setUrl(parameter.getValue());
            }
            if (StringUtils.endsWith(parameter.getName(), JDBC_USERNAME_SUFFIX)) {
                databaseCredentialItem.setUsername(parameter.getValue());
            }
            if (StringUtils.endsWith(parameter.getName(), JDBC_PASSWORD_SUFFIX)) {
                databaseCredentialItem.setPassword(parameter.getValue());
            }
        });
        return databaseCredentialItem;
    }

    public void shutdown() {
        this.client.shutdown();
        hasShutdown = true;
    }

}

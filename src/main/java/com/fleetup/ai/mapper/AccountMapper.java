package com.fleetup.ai.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025-06-30 16:03:46
 */
public interface AccountMapper {

    List<Map<String, Object>> fetchAccountByCondition(@Param("acctId") Integer acctId,
                                                      @Param("companyName") String companyName,
                                                      @Param("country") String country,
                                                      @Param("language") Integer language,
                                                      @Param("status") Integer status,
                                                      @Param("acctType") Integer acctType);

}

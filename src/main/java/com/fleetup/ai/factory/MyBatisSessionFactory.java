package com.fleetup.ai.factory;

import com.fleetup.ai.PropertiesKey;
import com.fleetup.ai.dto.DatabaseCredential;
import com.fleetup.ai.plugin.SystemsManagementPlugin;
import com.fleetup.ai.utils.PropertiesUtils;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.datasource.unpooled.UnpooledDataSource;
import org.apache.ibatis.mapping.Environment;
import org.apache.ibatis.session.AutoMappingBehavior;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.LocalCacheScope;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.apache.ibatis.transaction.TransactionFactory;
import org.apache.ibatis.transaction.jdbc.JdbcTransactionFactory;
import org.apache.ibatis.type.JdbcType;

import javax.sql.DataSource;
import java.util.function.Supplier;

/**
 * @Author: Scott Lau
 * @Description: session factory
 * @Date: 2019/8/15 16:32
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MyBatisSessionFactory {

    private static final String DOMAIN_PACKAGE = "com.fleetup.ai.entity";
    private static final String MAPPER_PACKAGE = "com.fleetup.ai.mapper";
    private static final int DEFAULT_MAX_RETRIES = 2;

    private static SqlSessionFactory sqlSessionFactory;
    private static SqlSession sqlSession;

    @Getter
    private static DataSource dataSource;

    static {
        initSessionFactory();
    }

    public static SqlSession session() {
        if (sqlSession == null) {
            initSession();
        }
        sqlSession.commit();
        return sqlSession;
    }

    private static DataSource dataSource() {
        SystemsManagementPlugin systemsManagementPlugin = SystemsManagementPlugin.getInstance();
        String ssmJdbcPath = PropertiesUtils.getProperty(PropertiesKey.SSM_PATH);
        DatabaseCredential credential = systemsManagementPlugin.getDatabaseCredentialItem(ssmJdbcPath);
        systemsManagementPlugin.shutdown();
        dataSource = new UnpooledDataSource("oracle.jdbc.driver.OracleDriver",
                credential.getUrl(), credential.getUsername(), credential.getPassword());
        return dataSource;
    }

    @SneakyThrows
    private static void initSession() {
        sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
    }

    @SneakyThrows
    public static void initSessionFactory() {
        if (sqlSessionFactory == null) {
            log.info("Try to create SqlSession...");
            TransactionFactory transactionFactory = new JdbcTransactionFactory();
            Environment environment = new Environment("development", transactionFactory, dataSource());
            Configuration config = new Configuration(environment);
            config.setCacheEnabled(false);
            config.setLocalCacheScope(LocalCacheScope.STATEMENT);
            config.setAutoMappingBehavior(AutoMappingBehavior.NONE);
            config.addMappers(MAPPER_PACKAGE);
            config.setJdbcTypeForNull(JdbcType.NULL);
            config.getTypeAliasRegistry().registerAliases(DOMAIN_PACKAGE);
            sqlSessionFactory = new SqlSessionFactoryBuilder().build(config);
            log.info("Create SqlSession success...");
        }
    }

    public static <T> T execute(Supplier<T> supplier, final int maxRetries) {
        if (maxRetries < 1) {
            return new RetryCommand<T>(DEFAULT_MAX_RETRIES).run(supplier);
        }
        return new RetryCommand<T>(maxRetries).run(supplier);
    }

    @RequiredArgsConstructor
    private static class RetryCommand<T> {

        private final int maxRetries;

        public T run(Supplier<T> function) {
            try {
                return function.get();
            } catch (Exception e) {
                return retry(function);
            }
        }

        private T retry(Supplier<T> supplier) throws RuntimeException {
            log.info("FAILED - Command failed, will be retried " + maxRetries + " times.");
            int retryCounter = 0;
            while (retryCounter < maxRetries) {
                try {
                    initSession();
                    return supplier.get();
                } catch (Exception e) {
                    retryCounter++;
                    log.info("FAILED - Command failed on retry " + retryCounter + " of " + maxRetries + " error: " + e);
                    if (retryCounter >= maxRetries) {
                        log.info("Max retries exceeded.");
                        break;
                    }
                }
            }
            throw new RuntimeException("Command failed on all of " + maxRetries + " retries");
        }
    }
}

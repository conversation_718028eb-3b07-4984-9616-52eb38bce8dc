package com.fleetup.ai;

import com.alibaba.fastjson2.JSON;
import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.fleetup.ai.handler.APIHandler;
import com.fleetup.ai.handler.FetchAccountByConditionHandler;
import com.fleetup.ai.handler.HandlerInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.io.InputStream;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2025-06-30 09:26:04
 */

public class LambdaRequestHandlerTest {

    @Test
    public void testHandle() {
        InputStream inputStream = getClass().getClassLoader().getResourceAsStream("payload.json");
        Map<String, Object> map = JSON.parseObject(inputStream, Map.class);
        new LambdaRequestHandler().handleRequest(map, null);
    }
}

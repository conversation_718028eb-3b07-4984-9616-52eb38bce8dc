openapi: 3.0.0
info:
  title: FleetUp System API
  version: 1.0.0
  description: APIs for obtain account, device, trip related information from the FleetUp system.
paths:
  /fetchAccountsByCondition:
    post:
      summary: Fetch accounts info by conditions.
      description: Get the list of account info. Returns accounts info that meet the conditions.
      operationId: fetchAccountsByCondition
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                acctId:
                  type: integer
                  description: account id
                companyName:
                  type: string
                  description: account company name
                country:
                  type: string
                  description: account country
                language:
                  type: integer
                  description: 1:English, 2:Spanish, 3:Portuguese
                status:
                  type: integer
                  description: 1:Active, 0:InActive
                acctType:
                  type: integer
                  description: 1:Billable Account, 2:Trial Account,3:QA Account, 4:Cancelled, 5:In Transition
      responses:
        "200":
          description: All account info in array.
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    acct_id:
                      type: integer
                      description: Unique ID of the account.
                    company_name:
                      type: string
                      description: Company name of the account.
                    country:
                      type: string
                      description: account country
                    language:
                      type: integer
                      description: 1:English, 2:Spanish, 3:Portuguese
                    address:
                      type: string
                    phone:
                      type: string
                    time_zone:
                      type: string
                      description: account's timezone
                    data_model:
                      type: integer
                      description: 1:US 2:EU
                    modality:
                      type: string
                    usdot_no:
                      type: string
                    status:
                      type: integer
                      description: 1:Active 2:InActive
                    acct_type:
                      type: integer
                      description: 1:Billable Account, 2:Trial Account,3:QA Account, 4:Cancelled, 5:In Transition
  /countAccountsByCondition:
    post:
      summary: Count accounts info by conditions.
      description: Get account number. Return account count that meet conditions.
      operationId: countAccountsByCondition
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                acctId:
                  type: integer
                  description: account id
                companyName:
                  type: string
                  description: account company name
                country:
                  type: string
                  description: account country
                language:
                  type: integer
                  description: 1:English, 2:Spanish, 3:Portuguese
                status:
                  type: integer
                  description: 1:Active, 0:InActive
                acctType:
                  type: integer
                  description: 1:Billable Account, 2:Trial Account,3:QA Account, 4:Cancelled, 5:In Transition
      responses:
        "200":
          description: Account count.
          content:
            application/json:
              schema:
                type: object
                properties:
                  count:
                    type: integer
                    description: The count of accounts that meet conditions.
  /getUserInfoByAcctId:
    get:
      summary: Get a list of user info by account id.
      description: Get the list of user info by account id. Return all users in the account.
      operationId: getUserInfoByAcctId
      parameters:
        - in: query
          name: acctId
          schema:
            type: integer
          required: true
          description: account id
      responses:
        "200":
          description: All user info in array.
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: integer
                      description: Unique ID of the user.
                    user_name:
                      type: string
                      description: User name of the user.
  /getDeviceInfoByAcctId:
    get:
      summary: Get a list of device info by account id.
      description: Get the device of user info by account id. Return all devices in the account.
      operationId: getDeviceInfoByAcctId
      parameters:
        - in: query
          name: acctId
          schema:
            type: integer
          required: true
          description: account id
      responses:
        "200":
          description: All device info in array.
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    devId:
                      type: string
                      description: The serial number of the device
                    deviceType:
                      type: string
                      description: Type of device